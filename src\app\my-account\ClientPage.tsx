"use client";

import {
  AccountSidebar,
  AccountSection,
} from "@/components/account/account-sidebar";
import { AccountDetailsForm } from "@/components/account/account-details-form";
import { PasswordChangeForm } from "@/components/account/password-change-form";
import { ProfilePictureUpload } from "@/components/account/profile-picture-upload";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useAuth, useRequireAuth } from "@/hooks/use-auth";
import { authUtils } from "@/lib/auth-utils";
import {
  AccountDetailsFormData,
  PasswordChangeFormData,
  getAccountCapabilities,
  getImageSourceType,
} from "@/lib/account-schemas";
import { getUserAccountData } from "@/utils/db/account-queries";
import { useEffect, useState } from "react";

export default function MyAccountPage() {
  const { user, isLoading } = useAuth();
  const { isAuthenticated } = useRequireAuth();

  const [activeSection, setActiveSection] =
    useState<AccountSection>("detalii-cont");
  const [accountData, setAccountData] = useState<any>(null);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);

  // Load account data
  useEffect(() => {
    const loadAccountData = async () => {
      if (!user?.id) return;

      try {
        setIsLoadingData(true);
        const data = await getUserAccountData(user.id);
        setAccountData(data);
      } catch (error) {
        console.error("Error loading account data:", error);
      } finally {
        setIsLoadingData(false);
      }
    };

    if (isAuthenticated && user?.id) {
      loadAccountData();
    }
  }, [isAuthenticated, user?.id]);

  const handleSignOut = async () => {
    try {
      await authUtils.signOut();
    } catch (error) {
      console.error("Sign out error:", error);
    }
  };

  const handleProfileUpdate = async (data: AccountDetailsFormData) => {
    setIsUpdating(true);
    try {
      const response = await fetch("/api/account/profile", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Eroare la actualizarea profilului");
      }

      // Refresh account data
      if (user?.id) {
        const updatedData = await getUserAccountData(user.id);
        setAccountData(updatedData);
      }

      alert("Profilul a fost actualizat cu succes!");
    } catch (error) {
      console.error("Error updating profile:", error);
      alert(error instanceof Error ? error.message : "A apărut o eroare");
    } finally {
      setIsUpdating(false);
    }
  };

  const handlePasswordChange = async (data: PasswordChangeFormData) => {
    setIsUpdating(true);
    try {
      const response = await fetch("/api/account/password", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Eroare la schimbarea parolei");
      }

      alert("Parola a fost schimbată cu succes!");
    } catch (error) {
      console.error("Error changing password:", error);
      alert(error instanceof Error ? error.message : "A apărut o eroare");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleImageUpload = async (file: File) => {
    setIsUpdating(true);
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch("/api/account/profile-picture", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Eroare la încărcarea imaginii");
      }

      // Refresh account data
      if (user?.id) {
        const updatedData = await getUserAccountData(user.id);
        setAccountData(updatedData);
      }

      alert("Imaginea de profil a fost încărcată cu succes!");
    } catch (error) {
      console.error("Error uploading image:", error);
      alert(error instanceof Error ? error.message : "A apărut o eroare");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleImageRemove = async () => {
    if (!accountData?.profilePicture?.id) return;

    setIsUpdating(true);
    try {
      const response = await fetch("/api/account/profile-picture", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ pictureId: accountData.profilePicture.id }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Eroare la ștergerea imaginii");
      }

      // Refresh account data
      if (user?.id) {
        const updatedData = await getUserAccountData(user.id);
        setAccountData(updatedData);
      }

      alert("Imaginea de profil a fost ștearsă cu succes!");
    } catch (error) {
      console.error("Error removing image:", error);
      alert(error instanceof Error ? error.message : "A apărut o eroare");
    } finally {
      setIsUpdating(false);
    }
  };

  if (isLoading || isLoadingData) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-portavio-orange mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Se încarcă...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  const capabilities = accountData
    ? getAccountCapabilities(accountData.accounts || [])
    : {
        canChangeEmail: false,
        canChangePassword: false,
        canChangeUsername: true,
        canChangeName: true,
        canUploadProfilePicture: true,
      };

  // Determine current image source
  const currentImage =
    accountData?.profilePicture?.imageData || accountData?.image || user?.image;

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-5xl font-bold text-foreground">Contul meu</h1>
            </div>
            <Button onClick={handleSignOut} variant="outline">
              Deconectează-te
            </Button>
          </div>

          <div className="flex flex-col lg:flex-row gap-8">
            {/* Sidebar */}
            <AccountSidebar
              activeSection={activeSection}
              onSectionChange={setActiveSection}
            />

            {/* Content Area */}
            <div className="flex-1">
              <Card className="shadow-lg">
                <CardHeader>
                  <CardTitle className="text-xl">
                    {activeSection === "detalii-cont" && "Detalii cont"}
                    {activeSection === "schimbare-parola" && "Schimbare parolă"}
                  </CardTitle>
                  <CardDescription>
                    {activeSection === "detalii-cont" &&
                      "Gestionează informațiile contului tău"}
                    {activeSection === "schimbare-parola" &&
                      "Actualizează parola contului tău"}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {activeSection === "detalii-cont" && accountData && (
                    <div className="space-y-8">
                      {/* Profile Picture Section */}
                      <ProfilePictureUpload
                        currentImage={currentImage}
                        username={accountData.username}
                        name={accountData.name}
                        onImageUpload={handleImageUpload}
                        onImageRemove={
                          getImageSourceType(currentImage) === "base64"
                            ? handleImageRemove
                            : undefined
                        }
                        isLoading={isUpdating}
                      />

                      {/* Account Details Form */}
                      <AccountDetailsForm
                        initialData={{
                          email: accountData.email,
                          username: accountData.username || "",
                          name: accountData.name,
                          createdAt: accountData.createdAt,
                        }}
                        capabilities={capabilities}
                        onSubmit={handleProfileUpdate}
                        isLoading={isUpdating}
                      />
                    </div>
                  )}

                  {activeSection === "schimbare-parola" && (
                    <PasswordChangeForm
                      onSubmit={handlePasswordChange}
                      isLoading={isUpdating}
                      canChangePassword={capabilities.canChangePassword}
                    />
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
