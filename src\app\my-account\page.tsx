import { auth } from "@/lib/auth";
import { getUserAccountData } from "@/utils/db/account-queries";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import MyAccountClientPage from "./ClientPage";

export default async function MyAccountPage() {
  // Get session on server side
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  console.log(session);

  // Redirect if not authenticated
  if (!session?.user) {
    redirect("/auth/signin");
  }

  // Fetch account data on server side
  let accountData = null;
  try {
    accountData = await getUserAccountData(session.user.id);
  } catch (error) {
    console.error("Error fetching account data:", error);
    // Continue with null data, client will handle the error state
  }

  console.log("acc data", accountData);

  return (
    <MyAccountClientPage initialAccountData={accountData} user={session.user} />
  );
}
