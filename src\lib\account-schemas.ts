import { z } from "zod";

// Account Details Schema
export const accountDetailsSchema = z.object({
  email: z.string().min(1, "Email-ul este obligatoriu").email("Email invalid"),
  username: z
    .string()
    .min(3, "Username-ul trebuie să aibă cel puțin 3 caractere")
    .max(30, "Username-ul nu poate avea mai mult de 30 de caractere")
    .regex(
      /^[a-zA-Z0-9_.]+$/,
      "Username-ul poate conține doar litere, cifre, puncte și underscore"
    )
    .refine(
      (username) => {
        const reservedUsernames = ["admin", "root", "system", "api", "www"];
        return !reservedUsernames.includes(username.toLowerCase());
      },
      {
        message: "Acest username nu este disponibil",
      }
    ),
  name: z
    .string()
    .min(1, "Numele este obligatoriu")
    .max(100, "Numele nu poate avea mai mult de 100 de caractere"),
});

// Password Change Schema
export const passwordChangeSchema = z
  .object({
    currentPassword: z.string().min(1, "Parola curentă este obligatorie"),
    newPassword: z
      .string()
      .min(8, "Parola nouă trebuie să aibă cel puțin 8 caractere")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        "Parola trebuie să conțină cel puțin o literă mică, o literă mare și o cifră"
      ),
    confirmNewPassword: z.string(),
  })
  .refine((data) => data.newPassword === data.confirmNewPassword, {
    message: "Parolele nu se potrivesc",
    path: ["confirmNewPassword"],
  });

// Profile Picture Upload Schema
export const profilePictureSchema = z.object({
  file: z
    .instanceof(File)
    .refine(
      (file) => file.size <= 5 * 1024 * 1024,
      "Fișierul nu poate depăși 5MB"
    )
    .refine(
      (file) =>
        ["image/jpeg", "image/jpg", "image/png", "image/webp"].includes(
          file.type
        ),
      "Doar fișierele JPEG, PNG și WebP sunt acceptate"
    ),
});

// TypeScript types
export type AccountDetailsFormData = z.infer<typeof accountDetailsSchema>;
export type PasswordChangeFormData = z.infer<typeof passwordChangeSchema>;
export type ProfilePictureFormData = z.infer<typeof profilePictureSchema>;

// User Account Data Types
export interface UserAccountData {
  id: string;
  email: string;
  username?: string;
  name: string;
  image?: string;
  createdAt: string;
  updatedAt: string;
  emailVerified: boolean;
  accounts: UserAccount[];
  profilePicture?: ProfilePicture;
  profile_pictures?: ProfilePicture[];
}

export interface UserAccount {
  id: string;
  providerId: string;
  accountId: string;
  createdAt: string;
  updatedAt: string;
}

export interface ProfilePicture {
  id: number;
  image_data: string;
  image_type: string;
  file_size: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Provider types
export type AuthProvider = "credential" | "google";

// Account capabilities based on provider
export interface AccountCapabilities {
  canChangeEmail: boolean;
  canChangePassword: boolean;
  canChangeUsername: boolean;
  canChangeName: boolean;
  canUploadProfilePicture: boolean;
}

// Helper function to determine account capabilities
export function getAccountCapabilities(
  accounts: UserAccount[]
): AccountCapabilities {
  const hasCredentialAccount = accounts.some(
    (account) => account.providerId === "credential"
  );

  return {
    canChangeEmail: hasCredentialAccount,
    canChangePassword: hasCredentialAccount,
    canChangeUsername: true, // Username can always be changed
    canChangeName: true, // Name can always be changed
    canUploadProfilePicture: true, // Profile picture can always be uploaded
  };
}

// Helper function to determine image source type
export function getImageSourceType(imageUrl?: string): "url" | "base64" | null {
  if (!imageUrl) return null;

  if (imageUrl.startsWith("data:image/")) {
    return "base64";
  }

  if (imageUrl.startsWith("http://") || imageUrl.startsWith("https://")) {
    return "url";
  }

  return null;
}
