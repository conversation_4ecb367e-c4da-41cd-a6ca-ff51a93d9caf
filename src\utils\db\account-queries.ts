import { hasuraQuery, hasuraMutation } from "./hasura";
import { UserAccountData, ProfilePicture } from "@/lib/account-schemas";

// GraphQL Queries

/**
 * Get user account data with accounts and profile picture
 */
export const GET_USER_ACCOUNT_DATA = `
  query GetUserAccountData($userId: String!) {
    user_by_pk(id: $userId) {
      id
      email
      username
      name
      image
      createdAt
      updatedAt
      emailVerified
      accounts {
        id
        providerId
        accountId
        createdAt
        updatedAt
      }
      profile_pictures(where: {is_active: {_eq: true}}, limit: 1) {
        id
        image_data
        image_type
        file_size
        is_active
        created_at
        updated_at
      }
    }
  }
`;

/**
 * Get user accounts to check provider information
 */
export const GET_USER_ACCOUNTS = `
  query GetUserAccounts($userId: String!) {
    account(where: {userId: {_eq: $userId}}) {
      id
      providerId
      accountId
      createdAt
      updatedAt
    }
  }
`;

/**
 * Get active profile picture for user
 */
export const GET_USER_PROFILE_PICTURE = `
  query GetUserProfilePicture($userId: String!) {
    profile_pictures(where: {user_id: {_eq: $userId}, is_active: {_eq: true}}, limit: 1) {
      id
      image_data
      image_type
      file_size
      is_active
      created_at
      updated_at
    }
  }
`;

// GraphQL Mutations

/**
 * Update user profile information
 */
export const UPDATE_USER_PROFILE = `
  mutation UpdateUserProfile($userId: String!, $updates: user_set_input!) {
    update_user_by_pk(pk_columns: {id: $userId}, _set: $updates) {
      id
      email
      username
      name
      image
      updatedAt
    }
  }
`;

/**
 * Insert new profile picture
 */
export const INSERT_PROFILE_PICTURE = `
  mutation InsertProfilePicture($object: profile_pictures_insert_input!) {
    insert_profile_pictures_one(object: $object) {
      id
      user_id
      image_data
      image_type
      file_size
      is_active
      created_at
      updated_at
    }
  }
`;

/**
 * Deactivate all profile pictures for user (before inserting new one)
 */
export const DEACTIVATE_USER_PROFILE_PICTURES = `
  mutation DeactivateUserProfilePictures($userId: String!) {
    update_profile_pictures(
      where: {user_id: {_eq: $userId}, is_active: {_eq: true}},
      _set: {is_active: false}
    ) {
      affected_rows
    }
  }
`;

/**
 * Delete profile picture
 */
export const DELETE_PROFILE_PICTURE = `
  mutation DeleteProfilePicture($id: Int!) {
    delete_profile_pictures_by_pk(id: $id) {
      id
    }
  }
`;

// Utility Functions

/**
 * Fetch complete user account data
 */
export async function getUserAccountData(userId: string): Promise<UserAccountData | null> {
  try {
    const result = await hasuraQuery<{ user_by_pk: UserAccountData | null }>(
      GET_USER_ACCOUNT_DATA,
      { variables: { userId } }
    );

    const userData = result.user_by_pk;
    if (!userData) return null;

    // Transform profile picture data
    if (userData.profile_pictures && userData.profile_pictures.length > 0) {
      const profilePic = userData.profile_pictures[0];
      userData.profilePicture = {
        id: profilePic.id,
        imageData: profilePic.image_data,
        imageType: profilePic.image_type,
        fileSize: profilePic.file_size,
        isActive: profilePic.is_active,
        createdAt: profilePic.created_at,
        updatedAt: profilePic.updated_at,
      };
    }

    // Remove the raw profile_pictures array
    delete (userData as any).profile_pictures;

    return userData;
  } catch (error) {
    console.error("Error fetching user account data:", error);
    throw new Error("Nu s-au putut încărca datele contului");
  }
}

/**
 * Update user profile
 */
export async function updateUserProfile(
  userId: string,
  updates: Partial<Pick<UserAccountData, "email" | "username" | "name">>
): Promise<UserAccountData> {
  try {
    const result = await hasuraMutation<{ update_user_by_pk: UserAccountData }>(
      UPDATE_USER_PROFILE,
      {
        variables: {
          userId,
          updates,
        },
      }
    );

    if (!result.update_user_by_pk) {
      throw new Error("Nu s-a putut actualiza profilul");
    }

    return result.update_user_by_pk;
  } catch (error) {
    console.error("Error updating user profile:", error);
    throw new Error("Nu s-a putut actualiza profilul");
  }
}

/**
 * Upload profile picture
 */
export async function uploadProfilePicture(
  userId: string,
  imageData: string,
  imageType: string,
  fileSize: number
): Promise<ProfilePicture> {
  try {
    // First, deactivate all existing profile pictures
    await hasuraMutation(DEACTIVATE_USER_PROFILE_PICTURES, {
      variables: { userId },
    });

    // Then insert the new profile picture
    const result = await hasuraMutation<{ insert_profile_pictures_one: any }>(
      INSERT_PROFILE_PICTURE,
      {
        variables: {
          object: {
            user_id: userId,
            image_data: imageData,
            image_type: imageType,
            file_size: fileSize,
            is_active: true,
          },
        },
      }
    );

    const profilePic = result.insert_profile_pictures_one;
    
    return {
      id: profilePic.id,
      imageData: profilePic.image_data,
      imageType: profilePic.image_type,
      fileSize: profilePic.file_size,
      isActive: profilePic.is_active,
      createdAt: profilePic.created_at,
      updatedAt: profilePic.updated_at,
    };
  } catch (error) {
    console.error("Error uploading profile picture:", error);
    throw new Error("Nu s-a putut încărca imaginea de profil");
  }
}

/**
 * Delete profile picture
 */
export async function deleteProfilePicture(pictureId: number): Promise<void> {
  try {
    await hasuraMutation(DELETE_PROFILE_PICTURE, {
      variables: { id: pictureId },
    });
  } catch (error) {
    console.error("Error deleting profile picture:", error);
    throw new Error("Nu s-a putut șterge imaginea de profil");
  }
}
